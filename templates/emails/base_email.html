{% load i18n %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE|default:'en' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block email_title %}{{ company_name }}{% endblock %}</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .email-header {
            background: linear-gradient(135deg, #3c3c3cff 0%, #0f0f0fff 100%);
            color: white;
            padding: 30px 40px;
            text-align: center;
        }
        
        .company-logo {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }
        
        .email-subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .email-body {
            padding: 40px;
        }
        
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        
        .content-section {
            margin-bottom: 25px;
        }
        
        .content-section p {
            margin-bottom: 15px;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .highlight-box {
            background-color: #f8f9fa;
            border-left: 4px solid #0f0f0fff;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .info-table th,
        .info-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            width: 35%;
        }
        
        .info-table td {
            color: #6c757d;
        }
        
        .info-table tr:last-child th,
        .info-table tr:last-child td {
            border-bottom: none;
        }
        
        .button {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #3c3c3cff 0%, #0f0f0fff 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 15px 0;
            transition: transform 0.2s ease;
        }
        
        .button:hover {
            transform: translateY(-1px);
        }
        
        .email-footer {
            background-color: #f8f9fa;
            padding: 30px 40px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer-text {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .contact-info {
            color: #495057;
            font-size: 14px;
            margin-top: 15px;
        }
        
        .signature {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .signature-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .signature-title {
            color: #6c757d;
            font-size: 14px;
        }
        
        /* Status badges */
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-new { background-color: #e3f2fd; color: #1976d2; }
        .status-review { background-color: #fff3e0; color: #f57c00; }
        .status-hired { background-color: #e8f5e8; color: #388e3c; }
        .status-rejected { background-color: #ffebee; color: #d32f2f; }
        
        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 0;
                box-shadow: none;
            }
            
            .email-header,
            .email-body,
            .email-footer {
                padding: 20px;
            }
            
            .info-table th,
            .info-table td {
                padding: 10px 15px;
                font-size: 14px;
            }
            
            .company-logo {
                font-size: 24px;
            }
            
            .greeting {
                font-size: 16px;
            }
            
            .content-section p {
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="company-logo">{{ company_name }}</div>
            <div class="email-subtitle">{% block email_subtitle %}{% trans "Recruitment Team" %}{% endblock %}</div>
        </div>
        
        <!-- Body -->
        <div class="email-body">
            <div class="greeting">
                {% block greeting %}
                {% trans "Dear" %} {{ candidate_firstname }},
                {% endblock %}
            </div>
            
            {% block email_content %}
            <!-- Email content goes here -->
            {% endblock %}
            
            <!-- Signature -->
            <div class="signature">
                <div class="signature-name">{{ company_name }} {% trans "Recruitment Team" %}</div>
                <div class="signature-title">{% trans "Human Resources Department" %}</div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="email-footer">
            <div class="footer-text">
                {% trans "If you have any questions, please don't hesitate to contact us." %}
            </div>
            {% if email_subject_key %}
            <div class="contact-info">
                {% trans "Reference ID:" %} <strong>{{ email_subject_key }}</strong>
            </div>
            {% endif %}
            <div class="contact-info">
                {% trans "This email was sent by" %} {{ company_name }}
            </div>
        </div>
    </div>
</body>
</html>
