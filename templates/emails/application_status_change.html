{% extends "emails/base_email.html" %}
{% load i18n %}

{% block email_title %}{% trans "Application Status Update" %} - {{ company_name }}{% endblock %}

{% block email_subtitle %}{% trans "Application Status Update" %}{% endblock %}

{% block email_content %}
<div class="content-section">
    <p>
        {% blocktrans with job_title=job_title %}
        We would like to inform you that your application for the position of <strong>{{ job_title }}</strong> has been updated.
        {% endblocktrans %}
    </p>
</div>

<div class="highlight-box">
    <table class="info-table">
        <tr>
            <th>{% trans "Position" %}</th>
            <td>{{ job_title }}</td>
        </tr>
        <tr>
            <th>{% trans "Application Status" %}</th>
            <td>
                <span class="status-badge {% if application_status == 'New' %}status-new{% elif 'Review' in application_status %}status-review{% elif application_status == 'Hired' %}status-hired{% elif application_status == 'Rejected' or application_status == 'Eliminated' %}status-rejected{% endif %}">
                    {{ application_status }}
                </span>
            </td>
        </tr>
        <tr>
            <th>{% trans "Application Date" %}</th>
            <td>{{ application.application_date|date:"F j, Y" }}</td>
        </tr>
        {% if application.application_state != 'New' %}
        <tr>
            <th>{% trans "Last Updated" %}</th>
            <td>{{ application.application_updated_at|date:"F j, Y g:i A" }}</td>
        </tr>
        {% endif %}
    </table>
</div>

{% if custom_message %}
<div class="content-section">
    <h3 style="color: #2c3e50; margin-bottom: 15px;">{% trans "Additional Information" %}</h3>
    <p>{{ custom_message|linebreaks }}</p>
</div>
{% endif %}

{% if application_status == 'Hired' %}
<div class="content-section">
    <p style="color: #388e3c; font-weight: 500;">
        🎉 {% trans "Congratulations! We are excited to welcome you to our team." %}
    </p>
    <p>
        {% trans "Our HR team will be in touch with you shortly regarding the next steps, including onboarding information and your start date." %}
    </p>
</div>
{% elif 'Review' in application_status %}
<div class="content-section">
    <p>
        {% trans "Your application is currently under review. We will keep you updated on any developments." %}
    </p>
    {% if application_status == 'Ready for Decision' %}
    <p style="color: #f57c00; font-weight: 500;">
        {% trans "Your application has reached the final stage of our selection process." %}
    </p>
    {% endif %}
</div>
{% elif application_status == 'Rejected' or application_status == 'Eliminated' %}
<div class="content-section">
    <p>
        {% trans "While we were impressed with your qualifications, we have decided to move forward with other candidates for this particular position." %}
    </p>
    <p>
        {% trans "We encourage you to apply for future opportunities that match your skills and experience. We will keep your profile in our talent database for consideration in upcoming roles." %}
    </p>
</div>
{% else %}
<div class="content-section">
    <p>
        {% trans "We will continue to review your application and keep you informed of any updates throughout the process." %}
    </p>
</div>
{% endif %}

<div class="content-section">
    <p>
        {% trans "Thank you for your interest in joining our team. We appreciate the time you have invested in the application process." %}
    </p>
</div>
{% endblock %}
