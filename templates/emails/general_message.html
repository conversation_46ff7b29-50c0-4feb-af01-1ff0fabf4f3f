{% extends "emails/base_email.html" %}
{% load i18n %}

{% block email_title %}{{ email_subject }} - {{ company_name }}{% endblock %}

{% block email_subtitle %}{% trans "Message from Recruitment Team" %}{% endblock %}

{% block email_content %}
{% if job_title %}
<div class="content-section">
    <p>
        {% blocktrans with job_title=job_title %}
        This message is regarding your application for the position of <strong>{{ job_title }}</strong>.
        {% endblocktrans %}
    </p>
</div>
{% endif %}

<div class="content-section">
    {{ email_body|linebreaks }}
</div>

{% if job_title %}
<div class="highlight-box">
    <table class="info-table">
        <tr>
            <th>{% trans "Position" %}</th>
            <td>{{ job_title }}</td>
        </tr>
        {% if application %}
        <tr>
            <th>{% trans "Application Date" %}</th>
            <td>{{ application.application_date|date:"F j, Y" }}</td>
        </tr>
        <tr>
            <th>{% trans "Current Status" %}</th>
            <td>
                <span class="status-badge {% if application.application_state == 'New' %}status-new{% elif 'Review' in application.application_state %}status-review{% elif application.application_state == 'Hired' %}status-hired{% elif application.application_state == 'Rejected' or application.application_state == 'Eliminated' %}status-rejected{% endif %}">
                    {{ application.application_state }}
                </span>
            </td>
        </tr>
        {% endif %}
    </table>
</div>
{% endif %}

<div class="content-section">
    <p>
        {% trans "Thank you for your continued interest in our company. We appreciate your patience throughout the application process." %}
    </p>
</div>
{% endblock %}
