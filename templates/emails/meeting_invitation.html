{% extends "emails/base_email.html" %}
{% load i18n %}

{% block email_title %}{% trans "Meeting Invitation" %} - {{ company_name }}{% endblock %}

{% block email_subtitle %}{% trans "Interview Invitation" %}{% endblock %}

{% block email_content %}
<div class="content-section">
    <p style="color: #388e3c; font-weight: 500; font-size: 18px;">
        🎉 {% trans "Great news! We would like to invite you for an interview." %}
    </p>
    <p>
        {% blocktrans with job_title=job_title %}
        We are pleased to invite you to an interview for the position of <strong>{{ job_title }}</strong>. 
        We were impressed with your application and would like to learn more about you.
        {% endblocktrans %}
    </p>
</div>

<div class="highlight-box">
    <h3 style="color: #2c3e50; margin-bottom: 20px; text-align: center;">
        📅 {% trans "Meeting Details" %}
    </h3>
    <table class="info-table">
        <tr>
            <th>{% trans "Meeting Title" %}</th>
            <td>{{ appointment.title }}</td>
        </tr>
        <tr>
            <th>{% trans "Position" %}</th>
            <td>{{ job_title }}</td>
        </tr>
        <tr>
            <th>{% trans "Interview Type" %}</th>
            <td>
                {% if appointment.appointment_kind == 'Video Call' %}
                    🎥 {% trans "Video Call" %}
                {% elif appointment.appointment_kind == 'Phone Call' %}
                    📞 {% trans "Phone Call" %}
                {% elif appointment.appointment_kind == 'In-Person' %}
                    🏢 {% trans "In-Person Meeting" %}
                {% else %}
                    {{ appointment.appointment_kind }}
                {% endif %}
            </td>
        </tr>
        <tr>
            <th>{% trans "Date & Time" %}</th>
            <td>
                <strong>{{ appointment.start_time|date:"l, F j, Y" }}</strong><br>
                {{ appointment.start_time|date:"g:i A" }} - {{ appointment.end_time|date:"g:i A" }}
                {% if appointment.start_time.tzinfo %}
                    ({{ appointment.start_time.tzinfo }})
                {% endif %}
            </td>
        </tr>
        <tr>
            <th>{% trans "Duration" %}</th>
            <td>
                {% with duration=appointment.end_time|timeuntil:appointment.start_time %}
                    {{ duration }}
                {% endwith %}
            </td>
        </tr>
        {% if appointment.interviewers %}
        <tr>
            <th>{% trans "Interviewer(s)" %}</th>
            <td>
                {% for interviewer in appointment.interviewers %}
                    👤 {{ interviewer }}{% if not forloop.last %}<br>{% endif %}
                {% endfor %}
            </td>
        </tr>
        {% endif %}
        {% if appointment.meeting_link %}
        <tr>
            <th>{% trans "Meeting Link" %}</th>
            <td>
                <a href="{{ appointment.meeting_link }}" class="button" style="color: white; text-decoration: none;">
                    🔗 {% trans "Join Meeting" %}
                </a>
                <br><small style="color: #6c757d;">{{ appointment.meeting_link }}</small>
            </td>
        </tr>
        {% endif %}
    </table>
</div>

{% if appointment.appointment_kind == 'Video Call' %}
<div class="content-section">
    <h4 style="color: #2c3e50;">💻 {% trans "Video Call Instructions" %}</h4>
    <ul style="margin-left: 20px; color: #495057;">
        <li>{% trans "Please test your camera and microphone before the meeting" %}</li>
        <li>{% trans "Ensure you have a stable internet connection" %}</li>
        <li>{% trans "Find a quiet, well-lit space for the interview" %}</li>
        <li>{% trans "Click the meeting link 5 minutes before the scheduled time" %}</li>
    </ul>
</div>
{% elif appointment.appointment_kind == 'Phone Call' %}
<div class="content-section">
    <h4 style="color: #2c3e50;">📞 {% trans "Phone Call Instructions" %}</h4>
    <ul style="margin-left: 20px; color: #495057;">
        <li>{% trans "Please ensure you're in a quiet location" %}</li>
        <li>{% trans "Have a good phone signal or use a landline if possible" %}</li>
        <li>{% trans "We will call you at the scheduled time" %}</li>
        <li>{% trans "Have your resume and any questions ready" %}</li>
    </ul>
</div>
{% elif appointment.appointment_kind == 'In-Person' %}
<div class="content-section">
    <h4 style="color: #2c3e50;">🏢 {% trans "In-Person Meeting Instructions" %}</h4>
    <ul style="margin-left: 20px; color: #495057;">
        <li>{% trans "Please arrive 10 minutes early" %}</li>
        <li>{% trans "Bring a printed copy of your resume" %}</li>
        <li>{% trans "Dress professionally" %}</li>
        <li>{% trans "Ask for directions at the reception if needed" %}</li>
    </ul>
</div>
{% endif %}

<div class="content-section">
    <h4 style="color: #2c3e50;">📋 {% trans "What to Expect" %}</h4>
    <p>
        {% trans "During the interview, we will discuss your background, experience, and how you can contribute to our team. This is also a great opportunity for you to ask questions about the role and our company culture." %}
    </p>
</div>

<div class="content-section">
    <h4 style="color: #2c3e50;">❓ {% trans "Need to Reschedule?" %}</h4>
    <p>
        {% trans "If you need to reschedule this interview, please contact us as soon as possible. When reaching out, please include the reference ID mentioned at the bottom of this email." %}
    </p>
</div>

<div class="content-section">
    <p style="color: #388e3c; font-weight: 500;">
        {% trans "We look forward to meeting with you and learning more about your qualifications!" %}
    </p>
</div>
{% endblock %}
