{% extends 'main.html'%}
{% load i18n %}
{%block content%}
<!-- Hidden CSRF token for JavaScript -->
{% csrf_token %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<div class="applicants-dashboard">
  <!-- Header Section -->
  <div class="dashboard-header">
    <h1>{% trans "Applicant Tracking" %}</h1>
    <div class="header-actions">
      <button id="refresh-applicants-btn" class="refresh-btn">
        <i class="fas fa-sync-alt"></i>
        {% trans "Refresh Applicants" %}
      </button>
      <div class="search-container">
        <form method="get" action="{% url 'people' %}">
          <input type="text" name="search" placeholder="{% trans 'Search applicants...' %}" class="search-input" value="{{ search_query }}">
          <button type="submit" class="search-btn">
            <i class="fas fa-search"></i>
          </button>
        </form>
      </div>
    </div>
  </div>

  <!-- Filter Section -->
  <div class="filter-section">
    <div class="filter-container">
      <div class="filter-group">
        <label for="position-filter">{% trans "Position" %}</label>
        <select id="position-filter" name="position" class="filter-dropdown" onchange="applyFilter('position', this.value)">
          <option value="">{% trans "All Positions" %}</option>
          {% for position in all_positions %}
          <option value="{{ position }}" {% if position_filter == position %}selected{% endif %}>{{ position }}</option>
          {% endfor %}
        </select>
      </div>

      <div class="filter-group">
        <label for="status-filter">{% trans "Status" %}</label>
        <select id="status-filter" name="status" class="filter-dropdown" onchange="applyFilter('status', this.value)">
          <option value="">{% trans "All Statuses" %}</option>
          {% for status in all_statuses %}
          <option value="{{ status }}" {% if status_filter == status %}selected{% endif %}>{{ status }}</option>
          {% endfor %}
        </select>
      </div>

      <div class="filter-group">
        <label for="location-filter">{% trans "Candidate Location" %}</label>
        <select id="location-filter" name="location" class="filter-dropdown" onchange="applyFilter('location', this.value)">
          <option value="">{% trans "All Locations" %}</option>
          {% for location in all_locations %}
          <option value="{{ location }}" {% if location_filter == location %}selected{% endif %}>{{ location }}</option>
          {% endfor %}
        </select>
      </div>

      <div class="filter-group date-filter">
        <label for="date-filter">{% trans "Application Date" %}</label>
        <select id="date-filter" name="date" class="filter-dropdown" onchange="applyFilter('date', this.value)">
          <option value="">{% trans "All Dates" %}</option>
          <option value="today" {% if date_filter == 'today' %}selected{% endif %}>{% trans "Today" %}</option>
          <option value="this-week" {% if date_filter == 'this-week' %}selected{% endif %}>{% trans "This Week" %}</option>
          <option value="this-month" {% if date_filter == 'this-month' %}selected{% endif %}>{% trans "This Month" %}</option>
          <option value="last-7-days" {% if date_filter == 'last-7-days' %}selected{% endif %}>{% trans "Last 7 Days" %}</option>
          <option value="last-30-days" {% if date_filter == 'last-30-days' %}selected{% endif %}>{% trans "Last 30 Days" %}</option>
          <option value="last-3-months" {% if date_filter == 'last-3-months' %}selected{% endif %}>{% trans "Last 3 Months" %}</option>
          <option value="last-6-months" {% if date_filter == 'last-6-months' %}selected{% endif %}>{% trans "Last 6 Months" %}</option>
        </select>
      </div>
    </div>

    <div class="active-filters">
      {% for filter_type, filter_value in active_filters %}
      <span class="filter-tag">{{ filter_value }} <a href="javascript:void(0)" onclick="removeFilter('{{ filter_type }}')" class="filter-remove"><i class="fas fa-times"></i></a></span>
      {% endfor %}
      {% if active_filters %}
      <a href="{% url 'people' %}" class="clear-filters">{% trans "Clear all filters" %}</a>
      {% endif %}
    </div>
  </div>

  {% if applicants %}
  <!-- Applicants Table -->
  <div class="applicants-table-container">
    <table class="applicants-table">
      <thead>
        <tr>
          <th>{% trans "Name" %} <i class="fas fa-sort"></i></th>
          <th>{% trans "Position" %} <i class="fas fa-sort"></i></th>
          <th>{% trans "Status" %} <i class="fas fa-sort"></i></th>
          <th>{% trans "Candidate Location" %} <i class="fas fa-sort"></i></th>
          <th>{% trans "Experience (Years)" %} <i class="fas fa-sort"></i></th>
          <th>{% trans "Score" %} <i class="fas fa-sort"></i></th>
          <th>{% trans "Applied On" %} <i class="fas fa-sort"></i></th>
          <th>{% trans "Action" %}</th>
        </tr>
      </thead>
      <tbody>
        {% for applicant in applicants %}
        <tr>
          <td class="applicant-info">
            <div style="background-color: {{ applicant.candidate_id.avatar_bg_color }}; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
              {{ applicant.candidate_id.candidate_firstname|slice:":1" }}{{ applicant.candidate_id.candidate_lastname|slice:":1" }}
            </div>
            <div>
              <p class="applicant-name">{{ applicant.candidate_id.full_name }}</p>
              <p class="applicant-email">{{ applicant.candidate_id.candidate_email }}</p>
            </div>
          </td>
          <td>{{ applicant.vacancy_id.vacancy_title }}</td>
          <td>
            {% with status=applicant.application_state %}
            <span class="status-badge status-{{ status|lower }}">{% trans status %}</span>
            {% endwith %}
          </td>
          <td>
            {% if applicant.cv_text_obj.is_cv_analyzed and applicant.cv_text_obj.ai_analysis_result.location %}
                  {{ applicant.cv_text_obj.ai_analysis_result.location }}
               {% else %}
                  {% trans "Not analyzed" %}
            {% endif %}
          </td>
          <td>
            {% if applicant.cv_text_obj.is_cv_analyzed and applicant.cv_text_obj.ai_analysis_result.total_experience %}
                  {% load experience_filters %}{{ applicant.cv_text_obj.ai_analysis_result.total_experience|years_only_with_plus }}
               {% else %}
                  {% trans "Not analyzed" %}
            {% endif %}
          </td>
          <td>
            {% if applicant.cv_text_obj.is_cv_analyzed and applicant.cv_text_obj.ai_analysis_result.score %}
                  {{ applicant.cv_text_obj.ai_analysis_result.score }}
               {% else %}
                  {% trans "Not analyzed" %}
            {% endif %}
          </td>
          <td>{{ applicant.application_date|date:"M d, Y" }}</td>
          <td>
            <a href="{% url 'application' application_id=applicant.application_id %}" class="view-btn">
              <i class="fas fa-eye"></i> {% trans "View" %}
            </a>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  {% else %}
  <!-- No Applicants Message -->
  <div class="no-applicants-container">
    <div class="no-applicants-content">
      <div class="no-applicants-icon">
        <i class="fas fa-users"></i>
      </div>
      <h3 class="no-applicants-title">{% trans "No Applicants Yet" %}</h3>
      <p class="no-applicants-message">
        {% trans "Nobody has applied to your job postings yet. Once candidates start applying, you'll see them here." %}
      </p>
      <div class="no-applicants-actions">
        <a href="{% url 'jobs' %}" class="btn-primary">
          <i class="fas fa-briefcase"></i> {% trans "View Job Postings" %}
        </a>
        <a href="{% url 'create' %}" class="btn-secondary">
          <i class="fas fa-plus"></i> {% trans "Create New Job" %}
        </a>
      </div>
    </div>
  </div>
  {% endif %}

  {% if applicants %}
  <!-- Pagination -->
  <div class="pagination-container">
    <div class="pagination-info">
      {% trans "Showing" %} <span class="current-range">{{ start_index }}-{{ end_index }}</span> {% trans "of" %} <span class="total-count">{{ total_applicants }}</span> {% trans "applicants" %}
    </div>
    <div class="pagination-controls">
      {% if applicants.has_previous %}
      <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ applicants.previous_page_number }}" class="pagination-btn">
        <i class="fas fa-chevron-left"></i>
      </a>
      {% else %}
      <button class="pagination-btn" disabled>
        <i class="fas fa-chevron-left"></i>
      </button>
      {% endif %}

      {% if applicants.number|add:"-2" > 0 %}
      <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ applicants.number|add:"-2" }}" class="pagination-btn">{{ applicants.number|add:"-2" }}</a>
      {% endif %}

      {% if applicants.has_previous %}
      <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ applicants.previous_page_number }}" class="pagination-btn">{{ applicants.previous_page_number }}</a>
      {% endif %}

      <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ applicants.number }}" class="pagination-btn pagination-active">{{ applicants.number }}</a>

      {% if applicants.has_next %}
      <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ applicants.next_page_number }}" class="pagination-btn">{{ applicants.next_page_number }}</a>
      {% endif %}

      {% if applicants.paginator.num_pages > applicants.number|add:"1" %}
      {% if applicants.paginator.num_pages > applicants.number|add:"2" %}
      <span class="pagination-ellipsis">...</span>
      <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ applicants.paginator.num_pages }}" class="pagination-btn">{{ applicants.paginator.num_pages }}</a>
      {% else %}
      <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ applicants.number|add:"2" }}" class="pagination-btn">{{ applicants.number|add:"2" }}</a>
      {% endif %}
      {% endif %}

      {% if applicants.has_next %}
      <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ applicants.next_page_number }}" class="pagination-btn">
        <i class="fas fa-chevron-right"></i>
      </a>
      {% else %}
      <button class="pagination-btn" disabled>
        <i class="fas fa-chevron-right"></i>
      </button>
      {% endif %}
    </div>
    <div class="per-page-selector">
      <form method="get" action="{% url 'people' %}" id="per-page-form">
        {% for key, value in request.GET.items %}
        {% if key != 'per_page' and key != 'page' %}
        <input type="hidden" name="{{ key }}" value="{{ value }}">
        {% endif %}
        {% endfor %}
        <label for="per-page">{% trans "Show" %}</label>
        <select id="per-page" name="per_page" class="per-page-dropdown" onchange="document.getElementById('per-page-form').submit()">
          <option value="5" {% if applicants_per_page == 5 %}selected{% endif %}>5</option>
          <option value="10" {% if applicants_per_page == 10 %}selected{% endif %}>10</option>
          <option value="20" {% if applicants_per_page == 20 %}selected{% endif %}>20</option>
          <option value="50" {% if applicants_per_page == 50 %}selected{% endif %}>50</option>
        </select>
        <span>{% trans "per page" %}</span>
      </form>
    </div>
  </div>
  {% endif %}
</div>

<!-- CSS Styles -->
<style>
  /* Main Dashboard Styles */
  .applicants-dashboard {
    padding: 2rem;
    background-color: #f8f9fa;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: #333;
    min-height: 100vh;
  }

  /* Header Styles */
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }

  .dashboard-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #1a202c;
  }

  .header-actions {
    display: flex;
    gap: 1rem;
  }

  .search-container {
    position: relative;
    width: 300px;
  }

  .search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    background-color: white;
    font-size: 0.875rem;
    outline: none;
    transition: all 0.2s;
  }

  .search-input:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  }

  .search-btn {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #94a3b8;
    cursor: pointer;
  }

  /* Refresh Button Styles */
  .refresh-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: #4f46e5;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    outline: none;
  }

  .refresh-btn:hover {
    background-color: #4338ca;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
  }

  .refresh-btn:active {
    transform: translateY(0);
  }

  .refresh-btn:disabled {
    background-color: #94a3b8;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .refresh-btn i {
    transition: transform 0.3s;
  }

  .refresh-btn.loading i {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  /* Filter Section Styles */
  .filter-section {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1rem;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
  }

  .filter-group label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #64748b;
    margin-bottom: 0.375rem;
    text-transform: uppercase;
  }

  .filter-dropdown, .date-input {
    padding: 0.625rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 0.875rem;
    color: #1a202c;
    outline: none;
    transition: all 0.2s;
  }

  .filter-dropdown:focus, .date-input:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  }

  .active-filters {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
  }

  .filter-tag {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem 0.75rem;
    background-color: #f1f5f9;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    color: #475569;
  }

  .filter-tag i {
    cursor: pointer;
    color: #94a3b8;
    transition: color 0.2s;
  }

  .filter-tag i:hover {
    color: #475569;
  }

  .clear-filters {
    border: none;
    background: none;
    color: #4f46e5;
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    padding: 0;
    transition: color 0.2s;
    text-decoration: none;
  }

  .clear-filters:hover {
    color: #4338ca;
    text-decoration: underline;
  }

  /* Applicants Table Styles */
  .applicants-table-container {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 1.5rem;
  }

  .applicants-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
  }

  .applicants-table th, .applicants-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
  }

  .applicants-table th {
    background-color: #f8fafc;
    font-weight: 600;
    color: #475569;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .applicants-table th i {
    margin-left: 0.375rem;
    font-size: 0.75rem;
    color: #94a3b8;
    cursor: pointer;
  }

  .applicants-table tbody tr {
    transition: background-color 0.2s;
  }

  .applicants-table tbody tr:hover {
    background-color: #f8fafc;
  }

  .applicant-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .applicant-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
  }

  .applicant-name {
    font-weight: 600;
    color: #1a202c;
    margin: 0 0 0.25rem 0;
  }

  .applicant-email {
    font-size: 0.75rem;
    color: #64748b;
    margin: 0;
  }

  /* Status Badge Styles */
  .status-badge {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
  }

  .status-new {
    background-color: #eff6ff;
    color: #2563eb;
  }

  .status-screening, .status-init_check {
    background-color: #eff6ff;
    color: #2563eb;
  }

  .status-interviewing, .status-phone, .status-online, .status-inface {
    background-color: #f0fdf4;
    color: #16a34a;
  }

  .status-evaluation {
    background-color: #fef3c7;
    color: #d97706;
  }

  .status-rejected {
    background-color: #fef2f2;
    color: #dc2626;
  }

  .status-signed, .status-hired {
    background-color: #ecfdf5;
    color: #059669;
  }

  .status-decision {
    background-color: #faf5ff;
    color: #7e22ce;
  }

  /* View Button Styles */
  .view-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 0.75rem;
    background-color: #3b82f6;
    color: white;
    text-decoration: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
  }

  .view-btn:hover {
    background-color: #2563eb;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .view-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .view-btn i {
    font-size: 0.75rem;
  }

  /* No Applicants Message Styles */
  .no-applicants-container {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 4rem 2rem;
    text-align: center;
    margin: 2rem 0;
  }

  .no-applicants-content {
    max-width: 500px;
    margin: 0 auto;
  }

  .no-applicants-icon {
    width: 80px;
    height: 80px;
    background-color: #f3f4f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
  }

  .no-applicants-icon i {
    font-size: 2rem;
    color: #9ca3af;
  }

  .no-applicants-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
  }

  .no-applicants-message {
    font-size: 1rem;
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 2rem;
  }

  .no-applicants-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  .btn-primary, .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
  }

  .btn-primary {
    background-color: #3b82f6;
    color: white;
  }

  .btn-primary:hover {
    background-color: #2563eb;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  }

  .btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover {
    background-color: #e5e7eb;
    color: #374151;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Checkbox Styles */
  input[type="checkbox"] {
    display: none;
  }

  input[type="checkbox"] + label {
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 2px solid #cbd5e1;
    border-radius: 4px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s;
  }

  input[type="checkbox"]:checked + label {
    background-color: #4f46e5;
    border-color: #4f46e5;
  }

  input[type="checkbox"]:checked + label:after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }

  /* Pagination Styles */
  .pagination-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
  }

  .pagination-info {
    font-size: 0.875rem;
    color: #64748b;
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .pagination-btn {
    min-width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e2e8f0;
    background-color: white;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #475569;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
  }

  .pagination-btn:hover:not(:disabled) {
    background-color: #f8fafc;
    border-color: #cbd5e1;
  }

  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .pagination-active {
    background-color: #4f46e5;
    border-color: #4f46e5;
    color: white;
  }

  .pagination-active:hover {
    background-color: #4338ca !important;
    border-color: #4338ca !important;
  }

  .pagination-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    height: 2.5rem;
    font-size: 0.875rem;
    color: #64748b;
  }

  .per-page-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #64748b;
  }

  .per-page-dropdown {
    padding: 0.375rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 0.875rem;
    color: #1a202c;
    outline: none;
  }

  /* Responsive Adjustments */
  @media (max-width: 1200px) {
    .filter-group {
      min-width: 160px;
    }
  }

  @media (max-width: 992px) {
    .applicants-dashboard {
      padding: 1.5rem;
    }

    .header-actions {
      flex-direction: column;
      gap: 0.75rem;
    }

    .search-container {
      width: 100%;
    }

    .applicants-table {
      display: block;
      overflow-x: auto;
    }

    .pagination-container {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .pagination-info, .pagination-controls, .per-page-selector {
      width: 100%;
      justify-content: center;
    }
  }

  @media (max-width: 768px) {
    .filter-container {
      flex-direction: column;
      gap: 1rem;
    }

    .filter-group {
      width: 100%;
    }
  }

  /* Empty state */
  .text-center {
    text-align: center;
  }

  .py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  /* Remove filter link */
  .filter-remove {
    color: inherit;
    text-decoration: none;
  }
</style>

<!-- JavaScript Functionality -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Sort functionality
    const sortIcons = document.querySelectorAll('th i.fa-sort');
    sortIcons.forEach(icon => {
      icon.addEventListener('click', function() {
        const column = this.parentElement.textContent.trim().toLowerCase().split(' ')[0];
        let currentUrl = new URL(window.location.href);
        let searchParams = currentUrl.searchParams;

        // Check if we're already sorting by this column
        const currentSort = searchParams.get('sort');
        const currentDirection = searchParams.get('direction');

        if (currentSort === column) {
          // Toggle direction
          searchParams.set('direction', currentDirection === 'asc' ? 'desc' : 'asc');
        } else {
          // Set new sort column and default to ascending
          searchParams.set('sort', column);
          searchParams.set('direction', 'asc');
        }

        // Reset to page 1 when sorting changes
        searchParams.set('page', '1');

        window.location.href = currentUrl.toString();
      });
    });
  });

  // Filter application function
  function applyFilter(filterType, value) {
    let currentUrl = new URL(window.location.href);
    let searchParams = currentUrl.searchParams;

    // Maintain search parameter if it exists
    const searchQuery = searchParams.get('search');

    // Clear all parameters
    searchParams = new URLSearchParams();

    // Re-add search if it exists
    if (searchQuery) {
      searchParams.set('search', searchQuery);
    }

    // Add the selected filter
    if (value) {
      searchParams.set(filterType, value);
    }

    // Keep other active filters
    const filterTypes = ['position', 'status', 'location', 'date'];
    filterTypes.forEach(type => {
      if (type !== filterType) {
        const filterValue = document.getElementById(`${type}-filter`).value;
        if (filterValue) {
          searchParams.set(type, filterValue);
        }
      }
    });

    // Reset to page 1 when filters change
    searchParams.set('page', '1');

    // Update URL
    currentUrl.search = searchParams.toString();
    window.location.href = currentUrl.toString();
  }

  // Remove filter function
  function removeFilter(filterType) {
    let currentUrl = new URL(window.location.href);
    let searchParams = currentUrl.searchParams;

    // Remove the specified filter
    searchParams.delete(filterType);

    // Update URL
    currentUrl.search = searchParams.toString();
    window.location.href = currentUrl.toString();
  }

  // Refresh applicants function
  function refreshApplicants() {
    console.log('Refresh applicants button clicked');

    const refreshBtn = document.getElementById('refresh-applicants-btn');
    if (!refreshBtn) {
      console.error('Refresh button not found');
      return;
    }

    console.log('Button found, starting processing...');

    // Show loading state
    refreshBtn.disabled = true;
    refreshBtn.classList.add('loading');
    refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> {% trans "Processing..." %}';

    // Get CSRF token - try multiple methods
    let csrfToken = '';
    const csrfInput = document.querySelector('[name=csrfmiddlewaretoken]');
    if (csrfInput) {
      csrfToken = csrfInput.value;
      console.log('CSRF token found from input:', csrfToken.substring(0, 10) + '...');
    } else {
      // Try to get from cookie
      const cookies = document.cookie.split(';');
      for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
          csrfToken = value;
          console.log('CSRF token found from cookie:', csrfToken.substring(0, 10) + '...');
          break;
        }
      }
    }

    if (!csrfToken) {
      console.warn('No CSRF token found, proceeding without it');
    }

    console.log('Making API call to:', '{% url "trigger_postjobfree_processing" %}');

    // Call the API to process PostJobFree applications
    fetch('{% url "trigger_postjobfree_processing" %}', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': csrfToken
      }
    })
    .then(response => {
      console.log('API response status:', response.status);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      console.log('API response data:', data);
      if (data.success) {
        // Show success message
        //alert(`{% trans "Success!" %} ${data.message}`);
        console.log('Processing successful, reloading page...');
        // Reload the page to show new applicants
        window.location.reload();
      } else {
        // Show error message
        console.error('API returned error:', data.message);
        alert(`{% trans "Error:" %} ${data.message}`);
      }
    })
    .catch(error => {
      console.error('Fetch error:', error);
      alert('{% trans "An error occurred while processing applications. Please try again." %}');
    })
    .finally(() => {
      console.log('Resetting button state...');
      // Reset button state
      refreshBtn.disabled = false;
      refreshBtn.classList.remove('loading');
      refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> {% trans "Refresh Applicants" %}';
    });
  }

  // Add event listener to refresh button
  document.addEventListener('DOMContentLoaded', function() {
    const refreshBtn = document.getElementById('refresh-applicants-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', refreshApplicants);
    }
  });
</script>
{%endblock content%}