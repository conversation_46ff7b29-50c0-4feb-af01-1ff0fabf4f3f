{% extends 'main.html' %} {% load static %} {% load i18n %} {% block content%}
<div class="container">
  <h1>{% trans "Job Requirements & Details" %}</h1>
  <div class="form-container">
    <div class="ai-section">
      <h2>{% trans "AI Assistant" %}</h2>
      <p>{% trans "Let AI suggest relevant skills and competitive salary range based on your role and location." %}</p>
      <button class="ai-suggest-btn" id="ai-suggest-btn">
        <span class="ai-icon">🤖</span>
        {% trans "Generate with AI" %}
      </button>
      <div class="ai-loading" id="ai-loading" style="display: none;">
        <span class="spinner"></span>
        {% trans "AI is generating suggestions..." %}
      </div>
    </div>

    <!-- Skills Section -->
    <div class="section-container">
      <h2>{% trans "Skills Requirements" %}</h2>
      <div class="skills-container">
        <label for="role-title">{% trans "Skill" %}</label>
        <div class="skills-input-group">
          <input
            type="text"
            id="skills-input"
            placeholder="{% trans 'e.g. JavaScript' %}"
          />
          <button class="add-btn" id="add-skill-btn">{% trans "Add" %}</button>
        </div>

        <label>{% trans "Choose Skills" %}</label>
        <div class="skill-chips">
          <div class="skill-chip" data-skill="JavaScript">JavaScript</div>
          <div class="skill-chip" data-skill="Python">Python</div>
          <div class="skill-chip" data-skill="Java">Java</div>
          <div class="skill-chip" data-skill="C++">C++</div>
          <div class="skill-chip" data-skill="C#">C#</div>
          <div class="skill-chip" data-skill="React">React</div>
          <div class="skill-chip" data-skill="Node.js">Node.js</div>
          <div class="skill-chip" data-skill="SQL">SQL</div>
          <div class="skill-chip" data-skill="AWS">AWS</div>
          <div class="skill-chip" data-skill="Docker">Docker</div>
        </div>

        <label>{% trans "Selected Skills" %}</label>
        <div class="selected-skills" id="selected-skills">
          <div class="empty-message" id="empty-skills-message">
            {% trans "No skills selected yet" %}
          </div>
        </div>
      </div>
    </div>

    <!-- Salary Section -->
    <div class="section-container">
      <h2>{% trans "Salary Details (Optional)" %}</h2>
      <div class="salary-grid">
        <div class="form-group">
          <label for="salary-min">{% trans "Minimum Salary" %}</label>
          <input
            type="number"
            id="salary-min"
            placeholder="{% trans 'Enter minimum salary' %}"
          />
        </div>
        <div class="form-group">
          <label for="salary-max">{% trans "Maximum Salary" %}</label>
          <input
            type="number"
            id="salary-max"
            placeholder="{% trans 'Enter maximum salary' %}"
          />
        </div>
        <div class="form-group">
          <label for="salary-currency">{% trans "Currency" %}</label>
          <select id="salary-currency">
            <option value="">{% trans "Select currency" %}</option>
            <option value="USD">USD</option>
            <option value="EUR">EUR</option>
            <option value="PLN">PLN</option>
            <option value="TRY">TRY</option>
            <option value="GBP">GBP</option>
            <option value="JPY">JPY</option>
            <option value="AUD">AUD</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Benefits Section -->
    <div class="section-container">
      <h2>{% trans "Benefits and Highlights (Optional)" %}</h2>
      <div class="skills-input-group">
        <input
          type="text"
          id="benefits-input"
          placeholder="{% trans 'e.g. Yearly Bonuses' %}"
        />
        <button class="add-btn" id="add-benefit-btn">{% trans "Add" %}</button>
      </div>

      <label>{% trans "Choose Benefits" %}</label>
      <div class="benefit-chips">
        <div class="benefit-chip" data-benefit="Dental Coverage">
          {% trans "Dental Coverage" %}
        </div>
        <div class="benefit-chip" data-benefit="Private Health Coverage">
          {% trans "Private Health Coverage" %}
        </div>
        <div class="benefit-chip" data-benefit="Gym membership">
          {% trans "Gym membership" %}
        </div>
        <div class="benefit-chip" data-benefit="Sign-in Bonus">
          {% trans "Sign-in Bonus" %}
        </div>
        <div class="benefit-chip" data-benefit="Relocation Package">
          {% trans "Relocation Package" %}
        </div>
        <div class="benefit-chip" data-benefit="Company Vehicle">
          {% trans "Company Vehicle" %}
        </div>
        <div class="benefit-chip" data-benefit="Food Card">{% trans "Food Card" %}</div>
        <div class="benefit-chip" data-benefit="Snacks & Coffee">
          {% trans "Snacks & Coffee" %}
        </div>
        <div class="benefit-chip" data-benefit="Pet Friendly Office">
          {% trans "Pet Friendly Office" %}
        </div>
      </div>

      <label>{% trans "Selected Benefits & Highlights" %}</label>
      <div class="selected-benefits" id="selected-benefits">
        <div class="empty-message" id="empty-benefits-message">
          {% trans "No benefits or highlights selected yet" %}
        </div>
      </div>
    </div>

    <div class="next-btn-container">
      <button class="back-btn" id="back-btn">{% trans "Back" %}</button>
      <button class="next-btn" id="next-btn">{% trans "Next" %}</button>
    </div>
  </div>
</div>

<style>
  :root {
    --primary: #4a6cf7;
    --primary-hover: #3859e9;
    --secondary: #f5f8ff;
    --text-color: #333;
    --border-color: #ddd;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    --ai-primary: #6366f1;
    --ai-primary-hover: #4f46e5;
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  }

  body {
    background-color: #f9fafc;
    color: var(--text-color);
    line-height: 1.6;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
  }

  h1, h2 {
    margin-bottom: 20px;
    color: #252b42;
  }

  h1 {
    font-size: 28px;
  }

  h2 {
    font-size: 20px;
    font-weight: 600;
  }

  .form-container {
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: var(--shadow);
  }

  .ai-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    text-align: center;
  }

  .ai-section h2 {
    color: white;
    margin-bottom: 10px;
  }

  .ai-section p {
    margin-bottom: 20px;
    opacity: 0.9;
  }

  .ai-suggest-btn {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
  }

  .ai-suggest-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
  }

  .ai-icon {
    margin-right: 8px;
    font-size: 18px;
  }

  .ai-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
    opacity: 0.9;
  }

  .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .section-container {
    margin-bottom: 40px;
    padding: 25px;
    background-color: #f8f9ff;
    border-radius: 8px;
    border-left: 4px solid var(--primary);
  }

  .form-group {
    margin-bottom: 24px;
  }

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 14px;
  }

  input, select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
  }

  input:focus, select:focus {
    outline: none;
    border-color: var(--primary);
  }

  .skills-container {
    margin-top: 20px;
  }

  .skills-input-group {
    display: flex;
    margin-bottom: 16px;
  }

  .skills-input-group input {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .add-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 0 20px;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .add-btn:hover {
    background-color: var(--primary-hover);
  }

  .skill-chips, .benefit-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
  }

  .skill-chip, .benefit-chip {
    background-color: var(--secondary);
    padding: 8px 16px;
    border-radius: 30px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
  }

  .skill-chip:hover {
    background-color: var(--primary);
    color: white;
  }

  .benefit-chip:hover {
    background-color: rgb(44, 44, 44);
    color: white;
  }

  .selected-skills, .selected-benefits {
    min-height: 100px;
    border: 1px dashed var(--border-color);
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
    background-color: #fafafa;
  }

  .selected-skill {
    display: inline-flex;
    align-items: center;
    background-color: var(--primary);
    color: white;
    padding: 6px 12px;
    border-radius: 30px;
    margin: 0 8px 8px 0;
    font-size: 14px;
  }

  .selected-benefit {
    display: inline-flex;
    align-items: center;
    background-color: rgb(44, 44, 44);
    color: white;
    padding: 6px 12px;
    border-radius: 30px;
    margin: 0 8px 8px 0;
    font-size: 14px;
  }

  .remove-skill, .remove-benefit {
    margin-left: 8px;
    cursor: pointer;
    font-size: 16px;
  }

  .salary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .next-btn-container {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
  }

  .next-btn, .back-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .back-btn {
    background-color: #6c757d;
  }

  .back-btn:hover {
    background-color: #5a6268;
  }

  .next-btn:hover {
    background-color: var(--primary-hover);
  }

  .empty-message {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 20px;
  }

</style>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Load basic information from sessionStorage
    const basicInfo = JSON.parse(sessionStorage.getItem("jobFormData") || "{}");
    
    // Skills functionality
    const skillsInput = document.getElementById("skills-input");
    const addSkillBtn = document.getElementById("add-skill-btn");
    const selectedSkillsContainer = document.getElementById("selected-skills");
    const emptySkillsMessage = document.getElementById("empty-skills-message");
    const skillChips = document.querySelectorAll(".skill-chip");

    let selectedSkills = [];

    function addSkill(skill) {
      if (!skill || selectedSkills.includes(skill)) return;
      selectedSkills.push(skill);
      updateSelectedSkillsDisplay();
      skillsInput.value = "";
    }

    function removeSkill(skill) {
      selectedSkills = selectedSkills.filter((s) => s !== skill);
      updateSelectedSkillsDisplay();
    }

    function updateSelectedSkillsDisplay() {
      if (selectedSkills.length === 0) {
        emptySkillsMessage.style.display = "block";
        selectedSkillsContainer.innerHTML = "";
        selectedSkillsContainer.appendChild(emptySkillsMessage);
        return;
      }

      emptySkillsMessage.style.display = "none";
      selectedSkillsContainer.innerHTML = "";

      selectedSkills.forEach((skill) => {
        const skillElement = document.createElement("div");
        skillElement.className = "selected-skill";
        skillElement.innerHTML = `
          ${skill}
          <span class="remove-skill" data-skill="${skill}">&times;</span>
        `;
        selectedSkillsContainer.appendChild(skillElement);
      });

      document.querySelectorAll(".remove-skill").forEach((btn) => {
        btn.addEventListener("click", function () {
          removeSkill(this.getAttribute("data-skill"));
        });
      });
    }

    addSkillBtn.addEventListener("click", function () {
      const skill = skillsInput.value.trim();
      addSkill(skill);
    });

    skillsInput.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        const skill = this.value.trim();
        addSkill(skill);
      }
    });

    skillChips.forEach((chip) => {
      chip.addEventListener("click", function () {
        const skill = this.getAttribute("data-skill");
        addSkill(skill);
      });
    });

    // Benefits functionality
    const benefitsInput = document.getElementById("benefits-input");
    const addBenefitBtn = document.getElementById("add-benefit-btn");
    const selectedBenefitsContainer = document.getElementById("selected-benefits");
    const emptyBenefitsMessage = document.getElementById("empty-benefits-message");
    const benefitChips = document.querySelectorAll(".benefit-chip");

    let selectedBenefits = [];

    function addBenefit(benefit) {
      if (!benefit || selectedBenefits.includes(benefit)) return;
      selectedBenefits.push(benefit);
      updateSelectedBenefitsDisplay();
      benefitsInput.value = "";
    }

    function removeBenefit(benefit) {
      selectedBenefits = selectedBenefits.filter((b) => b !== benefit);
      updateSelectedBenefitsDisplay();
    }

    function updateSelectedBenefitsDisplay() {
      if (selectedBenefits.length === 0) {
        emptyBenefitsMessage.style.display = "block";
        selectedBenefitsContainer.innerHTML = "";
        selectedBenefitsContainer.appendChild(emptyBenefitsMessage);
        return;
      }

      emptyBenefitsMessage.style.display = "none";
      selectedBenefitsContainer.innerHTML = "";

      selectedBenefits.forEach((benefit) => {
        const benefitElement = document.createElement("div");
        benefitElement.className = "selected-benefit";
        benefitElement.innerHTML = `
          ${benefit}
          <span class="remove-benefit" data-benefit="${benefit}">&times;</span>
        `;
        selectedBenefitsContainer.appendChild(benefitElement);
      });

      document.querySelectorAll(".remove-benefit").forEach((btn) => {
        btn.addEventListener("click", function () {
          removeBenefit(this.getAttribute("data-benefit"));
        });
      });
    }

    addBenefitBtn.addEventListener("click", function () {
      const benefit = benefitsInput.value.trim();
      addBenefit(benefit);
    });

    benefitsInput.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        const benefit = this.value.trim();
        addBenefit(benefit);
      }
    });

    benefitChips.forEach((chip) => {
      chip.addEventListener("click", function () {
        const benefit = this.getAttribute("data-benefit");
        addBenefit(benefit);
      });
    });

    // AI Suggestion functionality
    document.getElementById("ai-suggest-btn").addEventListener("click", function () {
      if (!basicInfo.roleTitle || !basicInfo.officeLocation) {
        alert("Please complete the basic information first (role title and location are required).");
        return;
      }

      const aiLoadingElement = document.getElementById("ai-loading");
      const aiSuggestBtn = document.getElementById("ai-suggest-btn");
      
      aiSuggestBtn.style.display = "none";
      aiLoadingElement.style.display = "flex";

      // Prepare data for AI API
      const requestData = {
        roleTitle: basicInfo.roleTitle,
        location: basicInfo.officeLocation,
        department: basicInfo.department || ''
      };

      // Call actual AI API
      fetch('/ai/generate_job_requirements/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(requestData)
      })
      .then(response => response.json())
      .then(data => {
        if (data.success && data.job_requirements) {
          const aiSuggestions = data.job_requirements;
          
          // Add AI suggested skills
          if (aiSuggestions.skills && Array.isArray(aiSuggestions.skills)) {
            aiSuggestions.skills.forEach(skill => addSkill(skill));
          }
          
          // Add AI suggested salary range
          if (aiSuggestions.salary) {
            if (aiSuggestions.salary.min) {
              document.getElementById("salary-min").value = aiSuggestions.salary.min;
            }
            if (aiSuggestions.salary.max) {
              document.getElementById("salary-max").value = aiSuggestions.salary.max;
            }
            if (aiSuggestions.salary.currency) {
              document.getElementById("salary-currency").value = aiSuggestions.salary.currency;
            }
          }
          
          
          aiLoadingElement.style.display = "none";
          aiSuggestBtn.style.display = "inline-block";
          aiSuggestBtn.innerHTML = '<span class="ai-icon">✅</span>AI Suggestions Applied';
          aiSuggestBtn.disabled = true;
        } else {
          throw new Error(data.message || 'Failed to generate AI suggestions');
        }
      })
      .catch(error => {
        console.error('Error calling AI API:', error);
        aiLoadingElement.style.display = "none";
        aiSuggestBtn.style.display = "inline-block";
        aiSuggestBtn.innerHTML = '<span class="ai-icon">❌</span>AI Error - Try Again';
        aiSuggestBtn.disabled = false;
        alert('Failed to generate AI suggestions. Please try again or fill the form manually.');
      });
    });

    // Function to get CSRF token from cookies
    function getCookie(name) {
      let cookieValue = null;
      if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim();
          if (cookie.substring(0, name.length + 1) === (name + '=')) {
            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
            break;
          }
        }
      }
      return cookieValue;
    }

    // Navigation
    document.getElementById("back-btn").addEventListener("click", function () {
      window.history.back();
    });

    document.getElementById("next-btn").addEventListener("click", function () {
      // Combine all form data
      const requirementsData = {
        ...basicInfo,
        skills: selectedSkills,
        salaryMin: document.getElementById("salary-min").value || 0,
        salaryMax: document.getElementById("salary-max").value || 0,
        salaryCurrency: document.getElementById("salary-currency").value || "",
        benefits: selectedBenefits,
      };

      sessionStorage.setItem("jobFormData", JSON.stringify(requirementsData));
      window.location.href = "{% url 'description' %}";
    });
  });
</script>
{% endblock %}