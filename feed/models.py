from django.utils import timezone
from django.db import models
import random
import string
import uuid


def generate_random_color():
    return "#" + "".join(random.choices(string.hexdigits[:-6], k=6))


class Employer(models.Model):
    employer_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    employer_name = models.CharField(max_length=255)
    employer_email = models.EmailField(max_length=255)
    employer_phone = models.Char<PERSON>ield(max_length=255, null=True)
    employer_address = models.CharField(max_length=255, null=True)
    office_locations = models.CharField(max_length=255, null=True)
    employer_created_at = models.DateTimeField(auto_now_add=True)
    employer_logo_url = models.CharField(max_length=255, null=True)
    employer_banner_url = models.Char<PERSON>ield(max_length=255, null=True)
    employer_website = models.CharField(max_length=255, null=False)
    employer_description = models.TextField(null=False)
    employer_industry = models.CharField(max_length=255, null=True)
    employer_headcount = models.IntegerField(null=True)
    employer_social_portals = models.TextField(null=True)
    employer_status = models.CharField(max_length=255, null=False, default="Active")


class Employee(models.Model):

    ROLES = (
        ("Administrator", "Administrator"),
        ("Hiring Manager", "Hiring Manager"),
        ("Interviewer", "Interviewer"),
        ("Read Only", "Read Only"),
        ("Recruiter", "Recruiter"),
    )

    # Base64 encoded default profile photo (10x10 transparent PNG)

    user = models.OneToOneField("auth.User", on_delete=models.CASCADE, primary_key=True)
    employer_id = models.ForeignKey(
        Employer, on_delete=models.CASCADE, db_column="employer_id"
    )
    role = models.CharField(
        max_length=100, null=True, default="Recruiter", db_index=True, choices=ROLES
    )  # Index for role filtering
    permissions = models.JSONField(null=True, blank=True)
    status = models.CharField(
        max_length=50, default="Active", db_index=True
    )  # Index for status filtering
    created_at = models.DateTimeField(auto_now_add=True)
    profile_photo = models.TextField(null=True, blank=True)
    last_activity_cleared = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when user last cleared their activity feed",
    )

    def __str__(self):
        return f"{self.user.username} - {self.user.email}"

    class Meta:
        indexes = [
            models.Index(
                fields=["employer_id", "role"]
            ),  # Composite index for employer's employees by role
            models.Index(
                fields=["employer_id", "status"]
            ),  # Composite index for employer's active employees
        ]


class Vacancy(models.Model):
    JOB_PORTAL_CHOICES = (
        ("Careers Page", "Careers Page"),
        ("LinkedIn", "LinkedIn"),
        ("Glassdoor", "Glassdoor"),
        ("Canvider", "Canvider"),
        ("Indeed", "Indeed"),
        ("Referral", "Referral"),
        ("Pracuj.pl", "Pracuj.pl"),
        ("JustJoinIT", "JustJoinIT"),
        ("NoFluffJobs", "NoFluffJobs"),
        ("RemoteOK", "RemoteOK"),
        ("StackOverflow", "StackOverflow"),
        ("GitHub", "GitHub"),
        ("Other", "Other"),
        ("Unknown", "Unknown"),
        ("jobloupe", "jobloupe"),
    )
    STATUS_CHOICES = (
        ("Active", "Active"),
        ("Draft", "Draft"),
        ("Closed", "Closed"),
        ("On-Hold", "On-Hold"),
        ("Archived", "Archived"),
        ("Reviewing", "Reviewing"),
        ("Deleted", "Deleted"),
    )
    vacancy_id = models.AutoField(primary_key=True)
    vacancy_status = models.CharField(
        default="Reviewing",
        choices=STATUS_CHOICES,
        max_length=255,
        db_index=True,  # Index for status filtering
    )
    vacancy_title = models.CharField(
        max_length=255, db_index=True
    )  # Index for title searches
    vacancy_country = models.CharField(max_length=255)
    vacancy_city = models.CharField(max_length=255)
    vacancy_creation_date = models.DateTimeField(
        auto_now_add=True, db_index=True
    )  # Index for date filtering
    number_of_applicants_temp = models.IntegerField(default=0)
    vacancy_bus_unit = models.CharField(
        max_length=255, null=False, default="IT", db_index=True
    )  # Index for department filtering
    employer_id = models.CharField(
        max_length=255, null=False, default=1, db_index=True
    )  # Keep as CharField for now due to view dependency
    work_schedule = models.CharField(max_length=255, null=False, default="Full Time")
    office_schedule = models.CharField(max_length=255, null=False, default="Remote")
    vacancy_job_description = models.TextField(null=True)
    skills = models.JSONField(null=True)
    job_portals = models.CharField(
        max_length=255, default="jobloupe", choices=JOB_PORTAL_CHOICES
    )
    salary_min = models.FloatField(null=True)
    salary_max = models.FloatField(null=True)
    salary_currency = models.CharField(max_length=255, null=True)
    jobtags = models.CharField(max_length=255, null=True)

    class Meta:
        indexes = [
            models.Index(
                fields=["vacancy_status", "vacancy_creation_date"]
            ),  # Composite index for active jobs with date
            models.Index(
                fields=["vacancy_city", "vacancy_country"]
            ),  # Composite index for location filtering
            models.Index(
                fields=["employer_id", "vacancy_status"]
            ),  # Composite index for employer's active jobs
        ]


class PotentialEmployer(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    company_name = models.CharField(max_length=255)
    contact_name = models.CharField(max_length=255)
    email = models.EmailField(max_length=255)
    phone = models.CharField(max_length=255)
    website = models.CharField(max_length=255)
    verification_info = models.TextField(null=True, max_length=500)
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=255, null=False, default="Pending")


class Candidate(models.Model):
    candidate_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    candidate_firstname = models.CharField(max_length=255)
    candidate_lastname = models.CharField(max_length=255)
    candidate_email = models.EmailField(max_length=255)
    candidate_phone = models.CharField(max_length=255)
    candidate_address = models.CharField(max_length=255, null=True)
    candidate_date_of_birth = models.DateField(null=True)
    candidate_created_at = models.DateTimeField(auto_now_add=True)
    avatar_bg_color = models.CharField(
        max_length=7,
        default=generate_random_color,
        editable=True,
        null=False,
        unique=False,
    )
    # Note: Candidates can apply to multiple employers, so we don't add employer_id here
    # Employer isolation is handled through Application -> Vacancy -> Employer relationship

    class Meta:
        indexes = [
            models.Index(fields=["candidate_email"]),  # Index for email searches
            models.Index(fields=["candidate_created_at"]),  # Index for date filtering
        ]

    @property
    def full_name(self):
        return f"{self.candidate_firstname} {self.candidate_lastname}"


class TalentPool(models.Model):
    talent_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    talent_firstname = models.CharField(max_length=255, null=False)
    talent_lastname = models.CharField(max_length=255, null=False)
    talent_email = models.EmailField(max_length=255, null=False)
    talent_phone = models.CharField(max_length=255, null=True)
    talent_country = models.CharField(max_length=255, null=True)
    talent_status = models.CharField(max_length=255, null=False, default="Active")
    cv_location = models.CharField(max_length=255, null=False)
    talent_added_at = models.DateTimeField(auto_now_add=True, null=False)
    employer_id = models.ForeignKey(
        Employer, on_delete=models.CASCADE, db_column="employer_id"
    )
    added_by = models.ForeignKey("auth.User", on_delete=models.CASCADE)

    class Meta:
        indexes = [
            models.Index(
                fields=["employer_id", "talent_added_at"]
            ),  # Index for employer filtering with date
            models.Index(
                fields=["employer_id", "talent_status"]
            ),  # Index for employer filtering with status
        ]

    @property
    def full_name(self):
        return f"{self.talent_firstname} {self.talent_lastname}"


class Application(models.Model):
    JOB_PORTAL_CHOICES = (
        ("Careers Page", "Careers Page"),
        ("LinkedIn", "LinkedIn"),
        ("Glassdoor", "Glassdoor"),
        ("Canvider", "Canvider"),
        ("Indeed", "Indeed"),
        ("Referral", "Referral"),
        ("Pracuj.pl", "Pracuj.pl"),
        ("JustJoinIT", "JustJoinIT"),
        ("NoFluffJobs", "NoFluffJobs"),
        ("RemoteOK", "RemoteOK"),
        ("StackOverflow", "StackOverflow"),
        ("GitHub", "GitHub"),
        ("PostJobFree", "PostJobFree"),
        ("Other", "Other"),
        ("Unknown", "Unknown"),
        ("jobloupe", "jobloupe"),
    )

    EDUCATION_LEVELS = (
        ("Primary Education", "Primary Education"),
        ("Lower Secondary Education", "Lower Secondary Education"),
        ("Bachelor's or Equivalent", "Bachelor's or Equivalent"),
        ("Master's or Equivalent", "Master's or Equivalent"),
        ("Doctorate or Equivalent", "Doctorate or Equivalent"),
        ("Vocational Training", "Vocational Training"),
        ("Postgraduate Education", "Postgraduate Education"),
        ("Other", "Other"),
        ("Unknown", "Unknown"),
    )

    application_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    candidate_id = models.ForeignKey(
        Candidate, on_delete=models.CASCADE, db_column="candidate_id"
    )
    vacancy_id = models.ForeignKey(
        Vacancy, on_delete=models.CASCADE, db_column="vacancy_id"
    )
    application_date = models.DateTimeField(
        auto_now_add=True, db_index=True
    )  # Index for date filtering
    application_source = models.CharField(
        max_length=255, default="jobloupe", choices=JOB_PORTAL_CHOICES
    )
    application_status = models.CharField(max_length=255, null=False, default="Active")
    application_state = models.CharField(
        max_length=255, null=False, default="New", db_index=True
    )  # Index for state filtering
    education_level = models.CharField(
        max_length=255, default="Unknown", choices=EDUCATION_LEVELS
    )
    notice_period = models.CharField(max_length=255, default="1 Month")
    current_position = models.CharField(max_length=255, default="Unknown")
    current_employer = models.CharField(max_length=255, default="Unknown")
    total_exp_years = models.FloatField(default=0.0)
    cv_location = models.CharField(max_length=255, null=True)
    score = models.IntegerField(default=-1, db_index=True)  # Index for score ordering

    class Meta:
        indexes = [
            models.Index(
                fields=["vacancy_id", "application_date"]
            ),  # Composite index for vacancy filtering with date
            models.Index(
                fields=["application_date", "application_state"]
            ),  # Composite index for feed queries
            models.Index(
                fields=["vacancy_id", "score"]
            ),  # Composite index for top applicants
        ]


class ApplicationState(models.Model):

    STATE_NAMES = [
        ("New", "New"),
        ("Review_1", "Review #1"),
        ("Review_2", "Review #2"),
        ("Review_3", "Review #3"),
        ("Review_4", "Review #4"),
        ("Review_5", "Review #5"),
        ("Decision", "Ready for Decision"),
        ("Eliminated", "Eliminated"),
        ("Offer", "Offer Made"),
        ("Accept", "Candidate Accepted"),
        ("Hired", "Hired"),
        ("Reject", "Candidate Rejected"),
    ]

    state_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    state_name = models.CharField(
        max_length=255, null=False, default="New", choices=STATE_NAMES
    )
    state_notes = models.TextField(null=True)
    state_started_at = models.DateTimeField(
        auto_now_add=True, db_index=True
    )  # Index for date filtering
    application_id = models.ForeignKey(
        Application, on_delete=models.CASCADE, db_column="application_id"
    )
    committed_by = models.ForeignKey(
        "auth.User",
        on_delete=models.SET_NULL,
        null=True
    )

    class Meta:
        indexes = [
            models.Index(
                fields=["application_id", "state_started_at"]
            ),  # Composite index for application state history
            models.Index(
                fields=["state_started_at", "state_name"]
            ),  # Composite index for activity feed
        ]


class ApplicationComment(models.Model):
    comment_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    application_id = models.ForeignKey(
        Application, on_delete=models.CASCADE, db_column="application_id"
    )
    commented_by = models.ForeignKey("auth.User", on_delete=models.CASCADE)
    comment_body = models.TextField()
    comment_date = models.DateTimeField(auto_now_add=True)


class Appointment(models.Model):
    APPOINTMENT_KINDS = (
        ("Phone Call", "Phone Call"),
        ("Video Call", "Video Call"),
        ("Online Interview", "Online Interview"),
        ("Technical Assessment", "Technical Assessment"),
        ("Final Interview", "Final Interview"),
        ("Face to Face Interview", "Face to Face Interview"),
        ("Office Visit", "Office Visit"),
        ("Other", "Other"),
    )

    appointment_kind = models.CharField(
        max_length=255, null=False, default="Phone Call", choices=APPOINTMENT_KINDS
    )
    title = models.CharField(max_length=255)
    start_time = models.DateTimeField(
        db_index=True
    )  # time already contains the date values in GMT.
    end_time = models.DateTimeField(
        db_index=True
    )  # time already contains the date values in GMT.
    created_by = models.ForeignKey("auth.User", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    invited_candidates = models.JSONField(null=True, blank=True)
    interviewers = models.JSONField(null=True, blank=True)
    meeting_link = models.CharField(max_length=255, null=True, blank=True)
    inform_invitees = models.BooleanField(default=True)
    meeting_status = models.CharField(max_length=255, null=False, default="Scheduled")
    color = models.IntegerField(default=1)
    vacancy_id = models.ForeignKey(
        Vacancy, on_delete=models.CASCADE, db_column="vacancy_id", null=True, blank=True
    )

    class Meta:
        indexes = [
            models.Index(
                fields=["start_time", "end_time"]
            ),  # Composite index for calendar queries
            models.Index(
                fields=["vacancy_id", "start_time"]
            ),  # Composite index for vacancy appointments
        ]

    def get_duration(self):
        return self.end_time - self.start_time

    def possible_appointment_kind(self):
        return self.appointment_kind

    def __str__(self):
        return f"{self.title} ({self.appointment_kind})"


class JobTemplate(models.Model):
    title = models.CharField(max_length=255)
    description = models.TextField()
    employer_id = models.ForeignKey(
        Employer, on_delete=models.CASCADE, db_column="employer_id"
    )
    created_by = models.ForeignKey("auth.User", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    usage_count = models.IntegerField(default=0)

    class Meta:
        indexes = [
            models.Index(
                fields=["employer_id", "created_at"]
            ),  # Index for employer filtering with date
            models.Index(
                fields=["employer_id", "title"]
            ),  # Index for employer filtering with title search
        ]

    def __str__(self):
        return f"{self.title} ({self.employer_id.employer_name})"


class Invitation(models.Model):
    INV_STATUS_OPT = (
        ("Pending", "Pending"),
        ("Accepted", "Accepted"),
        ("Declined", "Declined"),
        ("Expired", "Expired"),
        ("Canceled", "Canceled"),
    )

    ROLE_OPTIONS = (
        ("Administrator", "Administrator"),
        ("Recruiter", "Recruiter"),
        ("Hiring Manager", "Hiring Manager"),
        ("Interviewer", "Interviewer"),
        ("Read Only", "Read Only"),
    )

    first_name = models.CharField(max_length=100, null=False)
    last_name = models.CharField(max_length=100, null=False)
    token = models.CharField(max_length=64, unique=True)
    email = models.EmailField(unique=False, null=False)
    role = models.CharField(max_length=100, choices=ROLE_OPTIONS)
    invitation_status = models.CharField(
        max_length=20, default="Pending", choices=INV_STATUS_OPT
    )
    sent_date = models.DateTimeField(auto_now_add=True)
    employer_id = models.ForeignKey(
        Employer, on_delete=models.CASCADE, db_column="employer_id"
    )
    invited_by = models.ForeignKey("auth.User", on_delete=models.CASCADE)

    def default_expiry_date():
        return timezone.now() + timezone.timedelta(days=3)

    expiry_date = models.DateTimeField(default=default_expiry_date, null=True)

    class Meta:
        indexes = [
            models.Index(
                fields=["employer_id", "invitation_status"]
            ),  # Index for employer filtering with status
            models.Index(fields=["token"]),  # Index for token lookups
        ]

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.role}"

    def save(self, *args, **kwargs):
        if not self.token:
            self.token = "".join(
                random.choices(string.ascii_letters + string.digits, k=64)
            )
        super().save(*args, **kwargs)

    def is_expired(self):
        if self.expiry_date:
            return timezone.now() > self.expiry_date
        return False

    def set_expired(self):
        if self.is_expired():
            self.invitation_status = "Expired"
            self.save()
        else:
            self.invitation_status = self.invitation_status
            print("Invitation is not expired.")
            self.save()

    def cancel(self):
        self.invitation_status = "Canceled"
        self.save()


class TalentRequest(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    employer_id = models.ForeignKey(
        Employer, on_delete=models.CASCADE, db_column="employer_id"
    )
    vacancy_id = models.ForeignKey(
        Vacancy, on_delete=models.CASCADE, db_column="vacancy_id"
    )
    committed_by = models.ForeignKey("auth.User", on_delete=models.CASCADE)
    request_date = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=50, default="New")
    request_notes = models.TextField(null=True)
    request_type = models.CharField(max_length=50, default="Unknown", null=True)


class WorkSchedule(models.Model):
    employer_id = models.ForeignKey(
        Employer, on_delete=models.CASCADE, db_column="employer_id"
    )
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class OfficeSchedule(models.Model):
    employer_id = models.ForeignKey(
        Employer, on_delete=models.CASCADE, db_column="employer_id"
    )
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class OfficeLocation(models.Model):
    employer_id = models.ForeignKey(
        Employer, on_delete=models.CASCADE, db_column="employer_id"
    )
    city = models.CharField(max_length=100)
    country = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    location_details = models.JSONField(null=True, blank=True)

    def __str__(self):
        return f"{self.city}, {self.country}"


class Department(models.Model):
    employer_id = models.ForeignKey(
        Employer, on_delete=models.CASCADE, db_column="employer_id"
    )
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class ApplicationCvText(models.Model):
    cv_text_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    cv_text_date = models.DateTimeField(auto_now_add=True)
    cv_location = models.CharField(max_length=255, null=True)
    cv_text = models.TextField(null=True)
    application_id = models.ForeignKey(
        Application, on_delete=models.CASCADE, db_column="application_id"
    )
    is_cv_analyzed = models.BooleanField(default=False)
    ai_analysis_result = models.JSONField(null=True)

    def __str__(self):
        return self.cv_text


class PortalConfigurations(models.Model):

    PORTAL_NAMES = (
        ("LinkedIn", "LinkedIn"),
        ("Glassdoor", "Glassdoor"),
        ("Workloupe", "Workloupe"),
        ("Himalayas", "Himalayas"),
        ("PostJobFree", "PostJobFree"),
    )

    config_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    employer_id = models.ForeignKey(
        Employer, on_delete=models.CASCADE, db_column="employer_id"
    )
    portal_name = models.CharField(max_length=100, choices=PORTAL_NAMES)
    portal_url = models.CharField(max_length=255, null=True)
    portal_status = models.CharField(max_length=50, default="Inactive")
    config_json = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
