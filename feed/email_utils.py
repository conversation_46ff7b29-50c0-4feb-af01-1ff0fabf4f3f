from django.utils.translation import gettext as _
from django.utils import translation
from django.template.loader import render_to_string
from django.conf import settings
import os
import redmail


def get_user_language_from_request(request):
    """
    Get the user's preferred language from the request.
    Returns the language code or default language if not found.
    """
    # Check session first (highest priority)
    language = request.session.get('django_language')
    if language and is_valid_language(language):
        return language
        
    language = request.session.get('_language')
    if language and is_valid_language(language):
        return language
        
    language = request.session.get(settings.LANGUAGE_COOKIE_NAME)
    if language and is_valid_language(language):
        return language

    # Check cookies
    language = request.COOKIES.get('django_language')
    if language and is_valid_language(language):
        return language
        
    language = request.COOKIES.get(settings.LANGUAGE_COOKIE_NAME)
    if language and is_valid_language(language):
        return language

    # Return default language
    return settings.LANGUAGE_CODE


def is_valid_language(language):
    """Check if the language is in the list of available languages"""
    available_languages = [lang[0] for lang in settings.LANGUAGES]
    return language in available_languages


def render_email_template(template_name, context, language=None):
    """
    Render an email template with proper language activation.
    
    Args:
        template_name: Name of the template file
        context: Template context dictionary
        language: Language code to use for rendering
    
    Returns:
        Rendered HTML content
    """
    if language:
        # Activate the specified language for this rendering
        current_language = translation.get_language()
        translation.activate(language)
        
        try:
            rendered_content = render_to_string(template_name, context)
        finally:
            # Restore the original language
            translation.activate(current_language)
    else:
        rendered_content = render_to_string(template_name, context)
    
    return rendered_content


def send_email_with_template(template_name, context, subject, sender_name, sender_email, 
                           recipient_email, language=None, cc=None):
    """
    Send an email using a template with proper i18n support.
    
    Args:
        template_name: Name of the email template
        context: Template context dictionary
        subject: Email subject
        sender_name: Name of the sender
        sender_email: Email address of the sender
        recipient_email: Recipient email address
        language: Language code for the email
        cc: List of CC email addresses
    
    Returns:
        Boolean indicating success
    """
    try:
        # Get email configuration
        SMTP_MAIL_HOST = os.getenv("SMTP_MAIL_HOST")
        SMTP_MAIL_PORT = int(os.getenv("SMTP_MAIL_PORT"))
        MAIL_USERNAME = os.getenv("MAIL_USERNAME")
        MAIL_PASSWORD = os.getenv("MAIL_PASSWORD")

        # Activate language for email rendering
        if language:
            current_language = translation.get_language()
            translation.activate(language)
        
        try:
            # Render the email template
            html_content = render_email_template(template_name, context, language)
            
            # Create email sender
            email_sender = redmail.EmailSender(
                host=SMTP_MAIL_HOST,
                port=SMTP_MAIL_PORT,
                username=MAIL_USERNAME,
                password=MAIL_PASSWORD,
            )
            email_sender.connect()
            
            # Send the email
            email_sender.send(
                subject=subject,
                sender=f"{sender_name} <{MAIL_USERNAME}>",
                html=html_content,
                receivers=recipient_email,
                cc=cc if cc else []
            )
            
            return True
            
        finally:
            # Restore original language
            if language:
                translation.activate(current_language)
                
    except Exception as e:
        print(f"Error sending email: {str(e)}")
        return False


def get_email_context_base(candidate, employer, application=None, vacancy=None):
    """
    Get base context data that's common across all email types.

    Args:
        candidate: Candidate object
        employer: Employer object
        application: Application object (optional)
        vacancy: Vacancy object (optional)

    Returns:
        Dictionary with base context data
    """
    context = {
        'candidate': candidate,
        'candidate_name': f"{candidate.candidate_firstname} {candidate.candidate_lastname}",
        'candidate_firstname': candidate.candidate_firstname,
        'candidate_lastname': candidate.candidate_lastname,
        'candidate_email': candidate.candidate_email,
        'employer': employer,
        'employer_name': employer.employer_name,
        'company_name': employer.employer_name,
    }

    if application:
        context['application'] = application
        context['application_id'] = application.application_id
        context['email_subject_key'] = f"#A{application.application_id}"

    if vacancy:
        context['vacancy'] = vacancy
        context['job_title'] = vacancy.vacancy_title
        context['position_title'] = vacancy.vacancy_title

    return context


def send_application_status_email(candidate, employer, application, vacancy, custom_message=None, language=None):
    """
    Send an application status change email.

    Args:
        candidate: Candidate object
        employer: Employer object
        application: Application object
        vacancy: Vacancy object
        custom_message: Optional custom message from recruiter
        language: Language code for the email

    Returns:
        Boolean indicating success
    """
    context = get_email_context_base(candidate, employer, application, vacancy)
    context.update({
        'application_status': application.application_state,
        'custom_message': custom_message,
    })

    # Activate language for subject translation
    if language:
        current_language = translation.get_language()
        translation.activate(language)

    try:
        subject = _("Application Status Update - %(job_title)s") % {'job_title': vacancy.vacancy_title}
        if language:
            translation.activate(current_language)
    except:
        subject = f"Application Status Update - {vacancy.vacancy_title}"
        if language:
            translation.activate(current_language)

    return send_email_with_template(
        template_name='emails/application_status_change.html',
        context=context,
        subject=f"{subject} - {context['email_subject_key']}",
        sender_name=employer.employer_name,
        sender_email=os.getenv("MAIL_USERNAME"),
        recipient_email=candidate.candidate_email,
        language=language
    )


def send_meeting_invitation_email(candidate, employer, application, vacancy, appointment, language=None):
    """
    Send a meeting invitation email.

    Args:
        candidate: Candidate object
        employer: Employer object
        application: Application object
        vacancy: Vacancy object
        appointment: Appointment object
        language: Language code for the email

    Returns:
        Boolean indicating success
    """
    context = get_email_context_base(candidate, employer, application, vacancy)
    context.update({
        'appointment': appointment,
    })

    # Activate language for subject translation
    if language:
        current_language = translation.get_language()
        translation.activate(language)

    try:
        subject = _("Meeting Invitation - %(job_title)s") % {'job_title': vacancy.vacancy_title}
        if language:
            translation.activate(current_language)
    except:
        subject = f"Meeting Invitation - {vacancy.vacancy_title}"
        if language:
            translation.activate(current_language)

    return send_email_with_template(
        template_name='emails/meeting_invitation.html',
        context=context,
        subject=f"{subject} - {context['email_subject_key']}",
        sender_name=employer.employer_name,
        sender_email=os.getenv("MAIL_USERNAME"),
        recipient_email=candidate.candidate_email,
        language=language,
        cc=appointment.interviewers if hasattr(appointment, 'interviewers') else None
    )


def send_general_message_email(candidate, employer, application, vacancy, email_subject, email_body, language=None):
    """
    Send a general message email.

    Args:
        candidate: Candidate object
        employer: Employer object
        application: Application object
        vacancy: Vacancy object
        email_subject: Email subject
        email_body: Email body content
        language: Language code for the email

    Returns:
        Boolean indicating success
    """
    context = get_email_context_base(candidate, employer, application, vacancy)
    context.update({
        'email_subject': email_subject,
        'email_body': email_body,
    })

    return send_email_with_template(
        template_name='emails/general_message.html',
        context=context,
        subject=f"{email_subject} - {context['email_subject_key']}",
        sender_name=employer.employer_name,
        sender_email=os.getenv("MAIL_USERNAME"),
        recipient_email=candidate.candidate_email,
        language=language
    )


def send_application_confirmation_email(candidate, employer, vacancy, application_date=None, source=None, language=None):
    """
    Send an application confirmation email.

    Args:
        candidate: Candidate object
        employer: Employer object
        vacancy: Vacancy object
        application_date: Date of application
        source: Source of the application (e.g., "PostJobFree.com")
        language: Language code for the email

    Returns:
        Boolean indicating success
    """
    from datetime import datetime

    context = get_email_context_base(candidate, employer, vacancy=vacancy)
    context.update({
        'application_date': application_date or datetime.now(),
        'source': source,
    })

    # Activate language for subject translation
    if language:
        current_language = translation.get_language()
        translation.activate(language)

    try:
        subject = _("Application Received - %(job_title)s") % {'job_title': vacancy.vacancy_title}
        if language:
            translation.activate(current_language)
    except:
        subject = f"Application Received - {vacancy.vacancy_title}"
        if language:
            translation.activate(current_language)

    return send_email_with_template(
        template_name='emails/application_confirmation.html',
        context=context,
        subject=subject,
        sender_name=employer.employer_name,
        sender_email=os.getenv("MAIL_USERNAME"),
        recipient_email=candidate.candidate_email,
        language=language
    )


def send_team_invitation_email(invitation, employer_name, invitation_link, language=None):
    """
    Send a team invitation email.

    Args:
        invitation: Invitation object
        employer_name: Name of the employer/company
        invitation_link: Link to accept the invitation
        language: Language code for the email

    Returns:
        Boolean indicating success
    """
    context = {
        'company_name': employer_name,
        'invitation_first_name': invitation.first_name,
        'invitation_last_name': invitation.last_name,
        'invitation_role': invitation.role,
        'invitation_expiry': invitation.expiry_date,
        'invitation_link': invitation_link,
    }

    # Activate language for subject translation
    if language:
        current_language = translation.get_language()
        translation.activate(language)

    try:
        subject = _("Invitation: Join %(company_name)s at Canvider ATS") % {'company_name': employer_name}
        if language:
            translation.activate(current_language)
    except:
        subject = f"Invitation: Join {employer_name} at Canvider ATS"
        if language:
            translation.activate(current_language)

    return send_email_with_template(
        template_name='emails/team_invitation.html',
        context=context,
        subject=subject,
        sender_name="Canvider ATS Team",
        sender_email=os.getenv("MAIL_USERNAME"),
        recipient_email=invitation.email,
        language=language
    )
