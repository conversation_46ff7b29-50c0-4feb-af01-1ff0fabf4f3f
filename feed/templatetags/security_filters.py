"""
Custom template filters for secure HTML rendering
"""
from django import template
from django.utils.safestring import mark_safe
from django.utils.html import strip_tags, escape
import bleach
import re

register = template.Library()

# Allowed HTML tags for email content (safe subset)
ALLOWED_TAGS = [
    'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'span', 'div',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li',
    'a', 'img',
    'table', 'tr', 'td', 'th', 'thead', 'tbody',
    'blockquote', 'pre', 'code'
]

# Allowed attributes for HTML tags
ALLOWED_ATTRIBUTES = {
    'a': ['href', 'title'],
    'img': ['src', 'alt', 'width', 'height'],
    'span': ['style'],
    'div': ['style'],
    'p': ['style'],
    'table': ['style', 'border', 'cellpadding', 'cellspacing'],
    'td': ['style', 'colspan', 'rowspan'],
    'th': ['style', 'colspan', 'rowspan'],
}

# Allowed CSS properties (for style attributes)
ALLOWED_STYLES = [
    'color', 'background-color', 'font-size', 'font-weight', 'font-family',
    'text-align', 'text-decoration', 'margin', 'padding',
    'border', 'border-color', 'border-width', 'border-style',
    'width', 'height', 'max-width', 'max-height', 'line-height',
    'display', 'float', 'clear', 'vertical-align', 'text-transform',
    'letter-spacing', 'word-spacing', 'white-space'
]

@register.filter
def safe_html(value):
    """
    Safely render HTML content by sanitizing it with bleach
    Removes dangerous tags and attributes while preserving safe formatting
    """
    if not value:
        return ''

    # Clean the HTML with bleach (compatible with bleach 6.x)
    # Note: styles parameter was removed in bleach 6.x
    cleaned_html = bleach.clean(
        value,
        tags=ALLOWED_TAGS,
        attributes=ALLOWED_ATTRIBUTES,
        strip=True,  # Strip disallowed tags instead of escaping
        strip_comments=True
    )

    # Additional security: remove any remaining script-like content
    cleaned_html = re.sub(r'javascript:', '', cleaned_html, flags=re.IGNORECASE)
    cleaned_html = re.sub(r'on\w+\s*=', '', cleaned_html, flags=re.IGNORECASE)

    return mark_safe(cleaned_html)

@register.filter
def safe_email_html(value):
    """
    Safely render email HTML content with enhanced CSS support
    Designed specifically for email content display in history
    """
    if not value:
        return ''

    # Remove <style> blocks and <head> sections that cause display issues
    # but preserve inline styles
    cleaned_value = re.sub(r'<style[^>]*>.*?</style>', '', value, flags=re.DOTALL | re.IGNORECASE)
    cleaned_value = re.sub(r'<head[^>]*>.*?</head>', '', cleaned_value, flags=re.DOTALL | re.IGNORECASE)
    cleaned_value = re.sub(r'<!DOCTYPE[^>]*>', '', cleaned_value, flags=re.IGNORECASE)
    cleaned_value = re.sub(r'<html[^>]*>', '', cleaned_value, flags=re.IGNORECASE)
    cleaned_value = re.sub(r'</html>', '', cleaned_value, flags=re.IGNORECASE)
    cleaned_value = re.sub(r'<body[^>]*>', '', cleaned_value, flags=re.IGNORECASE)
    cleaned_value = re.sub(r'</body>', '', cleaned_value, flags=re.IGNORECASE)

    # Enhanced allowed attributes for email content
    email_allowed_attributes = {
        'a': ['href', 'title', 'style'],
        'img': ['src', 'alt', 'width', 'height', 'style'],
        'span': ['style', 'class'],
        'div': ['style', 'class'],
        'p': ['style', 'class'],
        'h1': ['style', 'class'],
        'h2': ['style', 'class'],
        'h3': ['style', 'class'],
        'h4': ['style', 'class'],
        'h5': ['style', 'class'],
        'h6': ['style', 'class'],
        'table': ['style', 'border', 'cellpadding', 'cellspacing', 'class'],
        'tr': ['style', 'class'],
        'td': ['style', 'colspan', 'rowspan', 'class'],
        'th': ['style', 'colspan', 'rowspan', 'class'],
        'thead': ['style', 'class'],
        'tbody': ['style', 'class'],
        'ul': ['style', 'class'],
        'ol': ['style', 'class'],
        'li': ['style', 'class'],
        'strong': ['style', 'class'],
        'b': ['style', 'class'],
        'em': ['style', 'class'],
        'i': ['style', 'class'],
        'u': ['style', 'class'],
        'br': [],
        'blockquote': ['style', 'class'],
        'pre': ['style', 'class'],
        'code': ['style', 'class'],
    }

    # Clean the HTML with enhanced attributes
    cleaned_html = bleach.clean(
        cleaned_value,
        tags=ALLOWED_TAGS,
        attributes=email_allowed_attributes,
        strip=True,
        strip_comments=True
    )

    # Additional security: remove any remaining script-like content
    cleaned_html = re.sub(r'javascript:', '', cleaned_html, flags=re.IGNORECASE)
    cleaned_html = re.sub(r'on\w+\s*=', '', cleaned_html, flags=re.IGNORECASE)

    # Wrap in a container with email-specific styling
    wrapped_html = f'''
    <div class="email-content-display" style="
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 100%;
        overflow-wrap: break-word;
        word-wrap: break-word;
    ">
        {cleaned_html}
    </div>
    '''

    return mark_safe(wrapped_html)

@register.filter
def safe_text(value):
    """
    Safely render plain text content with line breaks
    """
    if not value:
        return ''
    
    # Escape HTML and convert line breaks
    escaped = escape(value)
    with_breaks = escaped.replace('\n', '<br>')
    
    return mark_safe(with_breaks)

@register.filter
def truncate_safe_html(value, length=500):
    """
    Truncate HTML content safely while preserving basic formatting
    """
    if not value:
        return ''
    
    # First clean the HTML
    cleaned = safe_html(value)
    
    # Strip tags for length calculation
    text_only = strip_tags(cleaned)
    
    if len(text_only) <= length:
        return cleaned
    
    # Truncate and add ellipsis
    truncated_text = text_only[:length] + '...'
    
    # For truncated content, just return escaped text with breaks
    return safe_text(truncated_text)

@register.filter
def email_preview(value, length=200):
    """
    Generate a safe preview of email content
    """
    if not value:
        return ''
    
    # Strip all HTML tags for preview
    text_only = strip_tags(value)
    
    # Truncate and escape
    if len(text_only) > length:
        text_only = text_only[:length] + '...'
    
    return escape(text_only)
